'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { CustomerResponse } from '@/lib/validations';

interface DeleteCustomerDialogProps {
  customer: CustomerResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDelete: (id: string) => Promise<void>;
}

export function DeleteCustomerDialog({ customer, open, onOpenChange, onDelete }: DeleteCustomerDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!customer) return;

    setIsDeleting(true);
    try {
      await onDelete(customer.id);
      onOpenChange(false);
    } catch (error) {
      console.error('❌ Error deleting customer: ', error);
      // Error is handled in the hook
    } finally {
      setIsDeleting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isDeleting) {
      onOpenChange(newOpen);
    }
  };

  if (!customer) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delete Customer</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this customer? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border p-4 space-y-2">
            <div className="font-medium">{customer.name}</div>

            {customer.email && <div className="text-sm text-muted-foreground">{customer.email}</div>}

            {customer.phone && <div className="text-sm text-muted-foreground">{customer.phone}</div>}

            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Sessions:</span>
              <Badge variant={(customer.totalSessions || 0) > 0 ? 'default' : 'secondary'}>
                {customer.totalSessions || 0} sessions
              </Badge>
            </div>

            <div className="text-sm text-muted-foreground">
              Joined: {new Date(customer.createdAt).toLocaleDateString()}
            </div>
          </div>

          {(customer.totalSessions || 0) > 0 && (
            <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
              <div className="text-sm text-destructive">
                ⚠️ This customer has {customer.totalSessions || 0} unused sessions. Deleting will permanently remove
                their session balance.
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => handleOpenChange(false)} disabled={isDeleting}>
            Cancel
          </Button>
          <Button type="button" variant="destructive" onClick={handleDelete} disabled={isDeleting}>
            {isDeleting ? 'Deleting...' : 'Delete Customer'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
