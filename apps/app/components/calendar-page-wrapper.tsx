'use client';

import React, { createContext, useContext } from 'react';
import { startOfWeek, endOfWeek } from 'date-fns';
import { useCalendarContext } from '@/components/event-calendar/calendar-context';
import { useWorkoutsList, createWorkout, updateWorkout, addParticipant } from '@/hooks/use-workouts';

import { type CalendarEvent, CalendarDndProvider } from '@/components/event-calendar';
import type { CustomerResponse } from '@/lib/validations';
import { hasCustomerConflict, hasWorkoutTimeConflict, formatConflictMessage } from '@/lib/workout-utils';
import { toast } from 'sonner';

// Create a context for workout handlers
interface WorkoutHandlersContextType {
  handleWorkoutUpdate: (event: CalendarEvent) => Promise<void>;
  mutate: () => void;
}

const WorkoutHandlersContext = createContext<WorkoutHandlersContextType | undefined>(undefined);

export const useWorkoutHandlers = () => {
  const context = useContext(WorkoutHandlersContext);
  if (context === undefined) {
    throw new Error('useWorkoutHandlers must be used within a CalendarPageWrapper');
  }
  return context;
};

interface CalendarPageWrapperProps {
  children: React.ReactNode;
}

export function CalendarPageWrapper({ children }: CalendarPageWrapperProps) {
  const { currentDate } = useCalendarContext();

  // Calculate date range for fetching workouts (current week)
  const startDate = startOfWeek(currentDate, { weekStartsOn: 0 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 0 });

  // Fetch workouts for the current date range
  const { workouts, mutate } = useWorkoutsList({
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    limit: 100, // Maximum allowed by API
  });

  const handleWorkoutUpdate = async (event: CalendarEvent) => {
    try {
      if (!event.workoutId) return;

      const updatedWorkout = await updateWorkout(event.workoutId, {
        startTime: event.start.toISOString(),
        endTime: event.end.toISOString(),
      });

      if (updatedWorkout) {
        toast.success('Workout updated successfully');
        mutate(); // Refresh the workout list
      }
    } catch (error) {
      console.error('Error updating workout:', error);
      toast.error('Failed to update workout');
    }
  };

  const handleCustomerDrop = async (customer: CustomerResponse, targetDate: Date, targetTime?: Date) => {
    try {
      // Check if customer has sessions
      if ((customer.totalSessions || 0) <= 0) {
        toast.error('Customer has no sessions available');
        return;
      }

      // Calculate target workout time
      const targetDateTime = targetTime || targetDate;
      const workoutStart = new Date(targetDateTime);
      const workoutEnd = new Date(workoutStart);
      workoutEnd.setHours(workoutStart.getHours() + 1); // 1-hour duration

      // Check for customer conflicts
      const { hasConflict, conflictingWorkout } = hasCustomerConflict(customer.id, workoutStart, workoutEnd, workouts);

      if (hasConflict && conflictingWorkout) {
        toast.error(formatConflictMessage(customer.name, conflictingWorkout));
        return;
      }

      // Find if there's an existing workout that overlaps with the target time
      const existingWorkout = workouts.find((workout) => {
        const workoutStartTime = new Date(workout.startTime);
        const workoutEndTime = new Date(workout.endTime);

        // Check if the target time falls within the workout's time range
        // Target time should be >= workout start and < workout end
        return targetDateTime >= workoutStartTime && targetDateTime < workoutEndTime;
      });

      if (existingWorkout) {
        // Check if workout is at capacity
        const currentParticipants = existingWorkout.participantCount || 0;
        const maxParticipants = existingWorkout.maxParticipants || 5;

        if (currentParticipants >= maxParticipants) {
          toast.error('Workout is at maximum capacity');
          return;
        }

        // Add customer to existing workout
        const newParticipant = await addParticipant(existingWorkout.id, { customerId: customer.id });
        if (newParticipant) {
          toast.success(`${customer.name} added to ${existingWorkout.title}`);
        }
      } else {
        // Check for workout time conflicts before creating new workout
        const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
          workoutStart,
          workoutEnd,
          workouts
        );

        if (hasTimeConflict && conflictingWorkouts.length > 0) {
          const conflictingWorkout = conflictingWorkouts[0]!; // Safe because we checked length > 0
          const conflictTime = new Date(conflictingWorkout.startTime).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          });
          toast.error(
            `Cannot create workout: time slot conflicts with existing "${conflictingWorkout.title}" session at ${conflictTime}`
          );
          return;
        }

        // Create new workout and add customer
        const startTime = new Date(targetDateTime);
        const endTime = new Date(startTime);
        endTime.setHours(startTime.getHours() + 1); // 1-hour duration

        const newWorkout = await createWorkout({
          title: `Training Session`,
          description: '',
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          location: '',
          minParticipants: 1,
          maxParticipants: 5,
        });

        // Add customer to the new workout
        if (newWorkout) {
          const newParticipant = await addParticipant(newWorkout.id, { customerId: customer.id });
          if (newParticipant) {
            toast.success(`${customer.name} added to new workout`);
          }
        }
      }

      // Refresh the workout list to show updated data
      mutate();
    } catch (error) {
      console.error('Error handling customer drop:', error);
      toast.error('Failed to add customer to workout');
    }
  };

  return (
    <WorkoutHandlersContext.Provider value={{ handleWorkoutUpdate, mutate }}>
      <CalendarDndProvider onEventUpdate={handleWorkoutUpdate} onCustomerDrop={handleCustomerDrop}>
        {children}
      </CalendarDndProvider>
    </WorkoutHandlersContext.Provider>
  );
}
