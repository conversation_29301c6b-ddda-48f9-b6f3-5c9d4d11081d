'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RiAddLine, RiSearchLine } from '@remixicon/react';
import { MemberList } from './member-list';
import { InviteMemberDialog } from './invite-member-dialog';
import { AddTrainerDialog } from './add-trainer-dialog';

export function MemberManagement() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [isAddTrainerDialogOpen, setIsAddTrainerDialogOpen] = useState(false);

  return (
    <div className="space-y-6 py-5 px-4">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold">Members</h1>
          <p className="text-muted-foreground">Manage your organization&apos;s members and invite new trainers.</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <RiAddLine className="mr-2 h-4 w-4" />
            Invite Member
          </Button>
          <Button onClick={() => setIsAddTrainerDialogOpen(true)}>
            <RiAddLine className="mr-2 h-4 w-4" />
            Add Member
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search Members</CardTitle>
          <CardDescription>Find members by name or email</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <RiSearchLine className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search members..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <MemberList searchQuery={searchQuery} />

      <InviteMemberDialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen} />
      <AddTrainerDialog open={isAddTrainerDialogOpen} onOpenChange={setIsAddTrainerDialogOpen} />
    </div>
  );
}
