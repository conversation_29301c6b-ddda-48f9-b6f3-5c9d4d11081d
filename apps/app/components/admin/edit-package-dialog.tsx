'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { updatePackageSchema, type UpdatePackageInput, type PackageResponse } from '@/lib/validations';
import { updatePackage } from '@/hooks/use-packages';

interface EditPackageDialogProps {
  package_: PackageResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPackageChanged?: () => void;
}

export function EditPackageDialog({ package_, open, onOpenChange, onPackageChanged }: EditPackageDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<UpdatePackageInput>({
    resolver: zodResolver(updatePackageSchema),
  });

  const isActive = watch('isActive');

  useEffect(() => {
    if (package_) {
      reset({
        name: package_.name,
        description: package_.description || '',
        sessionCount: package_.sessionCount,
        price: parseFloat(package_.price),
        isActive: package_.isActive,
      });
    }
  }, [package_, reset]);

  const onSubmit = async (data: UpdatePackageInput) => {
    if (!package_) return;

    setIsSubmitting(true);
    try {
      const updatedPackage = await updatePackage(package_.id, data);
      if (updatedPackage) {
        toast.success('Package updated successfully');
        onOpenChange(false);
        if (onPackageChanged) {
          onPackageChanged();
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update package';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
    }
  };

  if (!package_) return null;

  const pricePerSession = parseFloat(package_.price) / package_.sessionCount;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Package</DialogTitle>
          <DialogDescription>Update package information, pricing, and availability.</DialogDescription>
        </DialogHeader>

        <div className="mb-4 p-3 bg-muted rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Current Package:</span>
            <Badge variant={package_.isActive ? 'default' : 'secondary'}>
              {package_.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>
          <div className="text-sm text-muted-foreground space-y-1">
            <div>Sessions: {package_.sessionCount}</div>
            <div>Price: RM {parseFloat(package_.price).toFixed(2)}</div>
            <div>Per Session: RM {pricePerSession.toFixed(2)}</div>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Package Name *</Label>
            <Input id="name" {...register('name')} placeholder="e.g., 10 Sessions Package" disabled={isSubmitting} />
            {errors.name && <p className="text-sm text-destructive">{errors.name.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Optional description of the package"
              disabled={isSubmitting}
              rows={3}
            />
            {errors.description && <p className="text-sm text-destructive">{errors.description.message}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sessionCount">Sessions *</Label>
              <Input
                id="sessionCount"
                type="number"
                min="1"
                max="100"
                {...register('sessionCount', { valueAsNumber: true })}
                placeholder="10"
                disabled={isSubmitting}
              />
              {errors.sessionCount && <p className="text-sm text-destructive">{errors.sessionCount.message}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">Price *</Label>
              <Input
                id="price"
                type="number"
                min="0"
                step="0.01"
                {...register('price', { valueAsNumber: true })}
                placeholder="600.00"
                disabled={isSubmitting}
              />
              {errors.price && <p className="text-sm text-destructive">{errors.price.message}</p>}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={isActive}
              onCheckedChange={(checked) => setValue('isActive', checked)}
              disabled={isSubmitting}
            />
            <Label htmlFor="isActive">Active (available for assignment)</Label>
          </div>

          {!isActive && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> Inactive packages cannot be assigned to new customers, but existing assignments
                remain valid.
              </p>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => handleOpenChange(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update Package'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
