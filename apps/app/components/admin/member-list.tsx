'use client';

import { useMembers } from '@/hooks/use-members';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { RiErrorWarningLine } from '@remixicon/react';

// TODO: add an invited lists
export function MemberList({ searchQuery }: { searchQuery: string }) {
  const { members, error } = useMembers({ search: searchQuery });

  if (error) {
    return (
      <div className="flex items-center justify-center rounded-lg border border-dashed p-8 text-center">
        <div className="flex flex-col items-center gap-2">
          <RiErrorWarningLine className="h-8 w-8 text-destructive" />
          <h2 className="text-lg font-semibold">Failed to load members</h2>
          <p className="text-sm text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Member List</CardTitle>
        <CardDescription>A list of all members in your organization.</CardDescription>
      </CardHeader>
      <CardContent>
        {members?.length == 0 ? (
          <div className="flex items-center justify-center rounded-lg border border-dashed p-8 text-center">
            <p className="text-sm text-muted-foreground">No members found</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {members?.map((member) => (
                <TableRow key={member.id}>
                  <TableCell>{member.name}</TableCell>
                  <TableCell>{member.email}</TableCell>
                  <TableCell>
                    <Badge className="capitalize">{member.role}</Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
