'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from '@workspace/auth';
import { LoaderFive } from '@/components/ui/loader';

interface OnboardingGuardProps {
  children: React.ReactNode;
}

export function OnboardingGuard({ children }: OnboardingGuardProps) {
  const { data: session, isPending } = useSession();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const response = await fetch('/api/trainer/profile');

        if (response.status === 404) {
          // User needs onboarding
          router.push('/onboarding');
          return;
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
      } finally {
        setIsChecking(false);
      }
    };

    checkOnboardingStatus();
  }, [session, isPending, router]);

  if (isPending || isChecking) {
    return (
      <div className="flex justify-center items-center h-screen text-blue-400">
        <LoaderFive text="Loading..." />
      </div>
    );
  }

  return <>{children}</>;
}
