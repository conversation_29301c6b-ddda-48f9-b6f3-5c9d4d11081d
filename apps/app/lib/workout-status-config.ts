import { EventColor } from '@/components/event-calendar/types';

export type WorkoutStatusKey = 'scheduled' | 'confirmed' | 'completed' | 'cancelled';

export interface WorkoutStatusInfo {
  key: WorkoutStatusKey;
  label: string;
  color: EventColor;
  cssClasses: string;
}

export const WORKOUT_STATUS_CONFIG: Record<WorkoutStatusKey, WorkoutStatusInfo> = {
  scheduled: {
    key: 'scheduled',
    label: 'Scheduled',
    color: 'blue',
    cssClasses:
      'bg-blue-200/50 hover:bg-blue-200/40 text-blue-900/90 dark:bg-blue-400/25 dark:hover:bg-blue-400/20 dark:text-blue-200 shadow-blue-700/8',
  },
  confirmed: {
    key: 'confirmed',
    label: 'Confirmed',
    color: 'violet',
    cssClasses:
      'bg-violet-200/50 hover:bg-violet-200/40 text-violet-900/90 dark:bg-violet-400/25 dark:hover:bg-violet-400/20 dark:text-violet-200 shadow-violet-700/8',
  },
  completed: {
    key: 'completed',
    label: 'Completed',
    color: 'emerald',
    cssClasses:
      'bg-emerald-200/50 hover:bg-emerald-200/40 text-emerald-900/90 dark:bg-emerald-400/25 dark:hover:bg-emerald-400/20 dark:text-emerald-200 shadow-emerald-700/8',
  },
  cancelled: {
    key: 'cancelled',
    label: 'Cancelled',
    color: 'rose',
    cssClasses:
      'bg-rose-200/50 hover:bg-rose-200/40 text-rose-900/90 dark:bg-rose-400/25 dark:hover:bg-rose-400/20 dark:text-rose-200 shadow-rose-700/8',
  },
};

// This will be used for the filter UI
export const filterableStatuses: WorkoutStatusInfo[] = [
  WORKOUT_STATUS_CONFIG.scheduled,
  WORKOUT_STATUS_CONFIG.confirmed,
  WORKOUT_STATUS_CONFIG.completed,
  WORKOUT_STATUS_CONFIG.cancelled,
];
