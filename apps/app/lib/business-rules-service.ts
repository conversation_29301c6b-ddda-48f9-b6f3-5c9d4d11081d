import { db, workouts, workoutParticipants, customers, users } from '@workspace/auth/server';
import { eq, and, count, sql } from 'drizzle-orm';
import { hasCustomerConflict, hasWorkoutTimeConflict } from './workout-utils';
import type { WorkoutResponse } from './validations';

export interface ValidationResult {
  valid: boolean;
  message: string;
  warnings?: string[];
}

export interface WorkoutValidationContext {
  trainerId: string;
  workoutId?: string; // For updates
  existingWorkouts?: WorkoutResponse[];
}

export interface ParticipantValidationContext {
  trainerId: string;
  workoutId: string;
  customerId: string;
  existingWorkouts?: WorkoutResponse[];
}

/**
 * Validates workout creation/update against business rules
 */
export async function validateWorkoutBusinessRules(
  workoutData: {
    title: string;
    startTime: Date;
    endTime: Date;
    minParticipants: number;
    maxParticipants: number;
  },
  context: WorkoutValidationContext
): Promise<ValidationResult> {
  const warnings: string[] = [];

  // Check if this is a rescheduling operation for a confirmed workout
  if (context.workoutId) {
    const [existingWorkout] = await db
      .select({
        status: workouts.status,
        startTime: workouts.startTime,
      })
      .from(workouts)
      .where(eq(workouts.id, context.workoutId));

    if (existingWorkout && existingWorkout.status === 'confirmed') {
      // Additional validation for confirmed workout rescheduling
      const originalTime = existingWorkout.startTime;
      const newTime = workoutData.startTime;

      // Check if the time is actually changing
      if (originalTime.getTime() !== newTime.getTime()) {
        // Allow rescheduling but add a warning about credit implications
        warnings.push('Rescheduling confirmed workout - participants have already been charged');

        // Check if rescheduling too close to the original time
        const timeDiff = Math.abs(newTime.getTime() - originalTime.getTime());
        const hoursDiff = timeDiff / (1000 * 60 * 60);

        if (hoursDiff < 2) {
          warnings.push('Rescheduling with less than 2 hours notice may cause participant confusion');
        }
      }
    }
  }

  // Rule 1: Validate participant limits (1-5 per session)
  if (workoutData.minParticipants < 1 || workoutData.minParticipants > 5) {
    return {
      valid: false,
      message: 'Minimum participants must be between 1 and 5',
    };
  }

  if (workoutData.maxParticipants < 1 || workoutData.maxParticipants > 5) {
    return {
      valid: false,
      message: 'Maximum participants must be between 1 and 5',
    };
  }

  if (workoutData.maxParticipants < workoutData.minParticipants) {
    return {
      valid: false,
      message: 'Maximum participants must be greater than or equal to minimum participants',
    };
  }

  // Rule 2: Validate 1-hour duration
  const durationMs = workoutData.endTime.getTime() - workoutData.startTime.getTime();
  const durationHours = durationMs / (1000 * 60 * 60);

  if (durationHours !== 1) {
    return {
      valid: false,
      message: 'Workout sessions must be exactly 1 hour long',
    };
  }

  // Rule 3: Validate 30-minute start time intervals
  const startMinutes = workoutData.startTime.getMinutes();
  if (startMinutes !== 0 && startMinutes !== 30) {
    return {
      valid: false,
      message: 'Workout sessions must start at 30-minute intervals (e.g., 10:00, 10:30)',
    };
  }

  // Rule 4: Check for trainer schedule conflicts
  if (context.existingWorkouts) {
    const { hasConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
      workoutData.startTime,
      workoutData.endTime,
      context.existingWorkouts,
      context.workoutId
    );

    if (hasConflict && conflictingWorkouts.length > 0) {
      const conflictingWorkout = conflictingWorkouts[0];
      if (conflictingWorkout) {
        const conflictTime = conflictingWorkout.startTime.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        });
        return {
          valid: false,
          message: `Time slot conflicts with existing "${conflictingWorkout.title}" session at ${conflictTime}`,
        };
      }
    }
  }

  // Rule 5: Validate business hours (optional warning)
  const hour = workoutData.startTime.getHours();
  if (hour < 6 || hour > 22) {
    warnings.push('Workout is scheduled outside typical business hours (6 AM - 10 PM)');
  }

  // Rule 6: Validate future scheduling
  const now = new Date();
  if (workoutData.startTime <= now) {
    return {
      valid: false,
      message: 'Workout must be scheduled for a future time',
    };
  }

  // Rule 7: Validate reasonable advance booking (warning)
  const advanceTimeMs = workoutData.startTime.getTime() - now.getTime();
  const advanceHours = advanceTimeMs / (1000 * 60 * 60);

  if (advanceHours < 2) {
    warnings.push('Workout is scheduled with less than 2 hours advance notice');
  }

  return {
    valid: true,
    message: 'Workout validation passed',
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

/**
 * Validates participant addition against business rules
 */
export async function validateParticipantBusinessRules(
  context: ParticipantValidationContext
): Promise<ValidationResult> {
  const { trainerId, workoutId, customerId, existingWorkouts } = context;

  // Rule 1: Verify customer belongs to trainer
  const [customer] = await db
    .select({
      id: customers.id,
      name: users.name,
      trainerId: customers.trainerId,
    })
    .from(customers)
    .innerJoin(users, eq(customers.userId, users.id))
    .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

  if (!customer) {
    return {
      valid: false,
      message: 'Customer not found or does not belong to this trainer',
    };
  }

  // Rule 2: Prevent zero-session customer scheduling
  // Import calculateCustomerTotalSessions at the top of the file
  const { calculateCustomerTotalSessions } = await import('./package-service');
  const totalSessions = await calculateCustomerTotalSessions(customerId);

  if (totalSessions <= 0) {
    return {
      valid: false,
      message: `${customer.name} has no session credits available`,
    };
  }

  // Rule 3: Check if customer is already enrolled
  const [existingParticipant] = await db
    .select()
    .from(workoutParticipants)
    .where(
      and(
        eq(workoutParticipants.workoutId, workoutId),
        eq(workoutParticipants.customerId, customerId),
        sql`${workoutParticipants.status} != 'cancelled'`
      )
    );

  if (existingParticipant) {
    return {
      valid: false,
      message: `${customer.name} is already enrolled in this workout`,
    };
  }

  // Rule 4: Check workout capacity
  const [workout] = await db
    .select({
      id: workouts.id,
      title: workouts.title,
      startTime: workouts.startTime,
      endTime: workouts.endTime,
      maxParticipants: workouts.maxParticipants,
      status: workouts.status,
    })
    .from(workouts)
    .where(and(eq(workouts.id, workoutId), eq(workouts.trainerId, trainerId)));

  if (!workout) {
    return {
      valid: false,
      message: 'Workout not found',
    };
  }

  // Check if workout is cancelled or completed
  if (workout.status === 'cancelled') {
    return {
      valid: false,
      message: 'Cannot add participants to a cancelled workout',
    };
  }

  if (workout.status === 'completed') {
    return {
      valid: false,
      message: 'Cannot add participants to a completed workout',
    };
  }

  // Get current participant count
  const participantCountResult = await db
    .select({ count: count() })
    .from(workoutParticipants)
    .where(and(eq(workoutParticipants.workoutId, workoutId), sql`${workoutParticipants.status} != 'cancelled'`));

  const currentParticipants = participantCountResult[0]?.count || 0;

  if (currentParticipants >= workout.maxParticipants) {
    return {
      valid: false,
      message: `Workout "${workout.title}" is at maximum capacity (${workout.maxParticipants} participants)`,
    };
  }

  // Rule 5: Check for customer schedule conflicts
  if (existingWorkouts) {
    const { hasConflict, conflictingWorkout } = hasCustomerConflict(
      customerId,
      workout.startTime,
      workout.endTime,
      existingWorkouts,
      workoutId
    );

    if (hasConflict && conflictingWorkout) {
      const conflictTime = new Date(conflictingWorkout.startTime).toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });
      return {
        valid: false,
        message: `${customer.name} has a conflicting workout "${conflictingWorkout.title}" at ${conflictTime}`,
      };
    }
  }

  // Rule 6: Validate enrollment timing
  const now = new Date();
  if (workout.startTime <= now) {
    return {
      valid: false,
      message: 'Cannot enroll in a workout that has already started',
    };
  }

  // Rule 7: Check if enrollment is too close to workout time
  const timeUntilWorkout = workout.startTime.getTime() - now.getTime();
  const hoursUntilWorkout = timeUntilWorkout / (1000 * 60 * 60);

  if (hoursUntilWorkout < 1) {
    return {
      valid: false,
      message: 'Cannot enroll in a workout starting within 1 hour',
    };
  }

  return {
    valid: true,
    message: 'Participant validation passed',
  };
}

/**
 * Validates workout confirmation against business rules
 */
export async function validateWorkoutConfirmation(workoutId: string, trainerId: string): Promise<ValidationResult> {
  // Get workout with participant count
  const [workout] = await db
    .select({
      id: workouts.id,
      title: workouts.title,
      minParticipants: workouts.minParticipants,
      maxParticipants: workouts.maxParticipants,
      status: workouts.status,
      startTime: workouts.startTime,
    })
    .from(workouts)
    .where(and(eq(workouts.id, workoutId), eq(workouts.trainerId, trainerId)));

  if (!workout) {
    return {
      valid: false,
      message: 'Workout not found',
    };
  }

  if (workout.status !== 'scheduled') {
    return {
      valid: false,
      message: `Cannot confirm workout with status: ${workout.status}`,
    };
  }

  // Get enrolled participant count
  const participantCountResult = await db
    .select({ count: count() })
    .from(workoutParticipants)
    .where(and(eq(workoutParticipants.workoutId, workoutId), eq(workoutParticipants.status, 'enrolled')));

  const enrolledParticipants = participantCountResult[0]?.count || 0;

  if (enrolledParticipants < workout.minParticipants) {
    return {
      valid: false,
      message: `Cannot confirm workout: need at least ${workout.minParticipants} participants (currently ${enrolledParticipants})`,
    };
  }

  // Check if workout is in the past
  const now = new Date();
  if (workout.startTime <= now) {
    return {
      valid: false,
      message: 'Cannot confirm a workout that has already started',
    };
  }

  return {
    valid: true,
    message: 'Workout can be confirmed',
  };
}

/**
 * Get capacity warnings for a workout
 */
export async function getWorkoutCapacityWarnings(workoutId: string, trainerId: string): Promise<string[]> {
  const warnings: string[] = [];

  const [workout] = await db
    .select({
      id: workouts.id,
      title: workouts.title,
      minParticipants: workouts.minParticipants,
      maxParticipants: workouts.maxParticipants,
      status: workouts.status,
    })
    .from(workouts)
    .where(and(eq(workouts.id, workoutId), eq(workouts.trainerId, trainerId)));

  if (!workout) {
    return warnings;
  }

  const participantCountResult = await db
    .select({ count: count() })
    .from(workoutParticipants)
    .where(and(eq(workoutParticipants.workoutId, workoutId), sql`${workoutParticipants.status} != 'cancelled'`));

  const currentParticipants = participantCountResult[0]?.count || 0;

  if (currentParticipants === 0) {
    warnings.push('No participants enrolled yet');
  } else if (currentParticipants < workout.minParticipants) {
    const needed = workout.minParticipants - currentParticipants;
    warnings.push(
      `Need ${needed} more participant${needed > 1 ? 's' : ''} to meet minimum (${currentParticipants}/${workout.minParticipants})`
    );
  } else if (currentParticipants === workout.maxParticipants) {
    warnings.push('Workout is at maximum capacity');
  } else if (currentParticipants === workout.maxParticipants - 1) {
    warnings.push('Workout is almost at capacity (1 spot remaining)');
  }

  return warnings;
}
