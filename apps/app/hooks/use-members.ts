'use client';

import useSWR, { mutate as globalMutate } from 'swr';
import { fetcher } from '@/lib/fetcher';
import { MemberQueryInput, MembersResponse, PaginatedResponse } from '@/lib/validations';

export function getMembersKey(params?: Partial<MemberQueryInput>) {
  const searchParams = new URLSearchParams();
  if (params?.search) searchParams.set('search', params.search);
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.offset) searchParams.set('offset', params.offset.toString());
  if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
  if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);
  return `/api/admin/members?${searchParams.toString()}`;
}

export function useMembers(params?: Partial<MemberQueryInput>) {
  const key = getMembersKey(params);
  const { data, error, isLoading, mutate } = useSWR<PaginatedResponse<MembersResponse>>(key, fetcher);

  return {
    members: data?.data || [],
    loading: isLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    pagination: data?.pagination || { total: 0, limit: 20, offset: 0, hasMore: false },
    mutate,
  };
}

export async function inviteMember(email: string, role: 'admin' | 'trainer') {
  const response = await fetch('/api/admin/members?action=invite', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, role }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to send invitation');
  }
  const newInvitation = await response.json();
  await globalMutate(getMembersKey());

  return newInvitation;
}

export async function addTrainer(name: string, email: string, phone?: string) {
  const response = await fetch('/api/admin/members?action=add', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ name, email, phone }),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Failed to add trainer');
  }
  const newTrainer = await response.json();
  await globalMutate(getMembersKey());

  return newTrainer;
}
