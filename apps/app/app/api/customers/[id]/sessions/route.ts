import { NextRequest } from 'next/server';
import { db, customers, users } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import {
  withAuth,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { calculateCustomerTotalSessions } from '@/lib/package-service';

// GET /api/customers/[id]/sessions - Get customer's total sessions
export const GET = withAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id: customerId } = await params;
      const trainerId = await getTrainerIdFromUser(userSession);

      if (!trainerId) {
        return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
      }

      // Verify customer belongs to trainer
      const [customer] = await db
        .select()
        .from(customers)
        .innerJoin(users, eq(customers.userId, users.id))
        .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

      if (!customer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      // Calculate total sessions from packages
      const totalSessions = await calculateCustomerTotalSessions(customerId);

      const response = {
        customerId,
        totalSessions,
        customerName: customer.users.name,
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);
