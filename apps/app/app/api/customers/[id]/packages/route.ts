import { NextRequest } from 'next/server';
import { db, customers } from '@workspace/auth/server';
import { eq, and } from 'drizzle-orm';
import {
  assignPackageSchema,
  customerPackageQuerySchema,
  type PaginatedResponse,
  type CustomerPackageResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { assignPackageToCustomer, getCustomerPackages } from '@/lib/package-service';

// GET /api/customers/[id]/packages - Get customer's packages
export const GET = withAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id: customerId } = await params;
      const trainerId = await getTrainerIdFromUser(userSession);

      if (!trainerId) {
        return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
      }

      // Verify customer belongs to trainer
      const [customer] = await db
        .select()
        .from(customers)
        .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

      if (!customer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      const queryValidation = validateQueryParams(request, customerPackageQuerySchema);
      if (!queryValidation.success) {
        console.log('❌ /api/customers/[id]/packages', queryValidation.error);

        return createErrorResponse(
          queryValidation.error.error,
          queryValidation.error.message,
          400,
          queryValidation.error.details
        );
      }

      const { includeExpired } = queryValidation.data;

      // Get customer packages
      let customerPackagesList = await getCustomerPackages(customerId, trainerId);

      // Filter out expired packages if requested
      if (!includeExpired) {
        customerPackagesList = customerPackagesList.filter((pkg) => pkg.sessionsRemaining > 0);
      }

      const response: PaginatedResponse<CustomerPackageResponse> = {
        data: customerPackagesList.map((pkg) => ({
          id: pkg.id,
          customerId: pkg.customerId,
          packageId: pkg.packageId,
          packageName: pkg.packageName,
          packageDescription: pkg.packageDescription,
          originalSessionCount: pkg.originalSessionCount,
          sessionsRemaining: pkg.sessionsRemaining,
          purchaseDate: pkg.purchaseDate,
          createdAt: pkg.createdAt,
          updatedAt: pkg.updatedAt,
        })),
        pagination: {
          total: customerPackagesList.length,
          limit: customerPackagesList.length,
          offset: 0,
          hasMore: false,
        },
      };

      return createSuccessResponse(response);
    } catch (error) {
      return handleApiError(error);
    }
  }
);

// POST /api/customers/[id]/packages - Assign package to customer
export const POST = withAuth(
  async (request: NextRequest, userSession, { params }: { params: Promise<{ id: string }> }) => {
    try {
      const { id: customerId } = await params;
      const trainerId = await getTrainerIdFromUser(userSession);

      if (!trainerId) {
        return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
      }

      // Verify customer belongs to trainer
      const [customer] = await db
        .select()
        .from(customers)
        .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

      if (!customer) {
        return createErrorResponse('Not Found', 'Customer not found', 404);
      }

      const bodyValidation = await validateRequestBody(request, assignPackageSchema);
      if (!bodyValidation.success) {
        return createErrorResponse(
          bodyValidation.error.error,
          bodyValidation.error.message,
          400,
          bodyValidation.error.details
        );
      }

      const packageData = bodyValidation.data;

      // Assign package to customer
      const newCustomerPackage = await assignPackageToCustomer(customerId, packageData, trainerId);

      if (!newCustomerPackage) {
        throw new Error('Failed to assign package to customer');
      }

      // Get the full package details for response
      const customerPackagesList = await getCustomerPackages(customerId, trainerId);
      const assignedPackage = customerPackagesList.find((pkg) => pkg.id === newCustomerPackage.id);

      if (!assignedPackage) {
        throw new Error('Failed to retrieve assigned package details');
      }

      const response: CustomerPackageResponse = {
        id: assignedPackage.id,
        customerId: assignedPackage.customerId,
        packageId: assignedPackage.packageId,
        packageName: assignedPackage.packageName,
        packageDescription: assignedPackage.packageDescription,
        originalSessionCount: assignedPackage.originalSessionCount,
        sessionsRemaining: assignedPackage.sessionsRemaining,
        purchaseDate: assignedPackage.purchaseDate,
        createdAt: assignedPackage.createdAt,
        updatedAt: assignedPackage.updatedAt,
      };

      return createSuccessResponse(response, 201);
    } catch (error) {
      return handleApiError(error);
    }
  }
);
