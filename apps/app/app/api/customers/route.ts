import { NextRequest } from 'next/server';
import { db, customers, users } from '@workspace/auth/server';
import { eq, and, ilike, asc, desc, count } from 'drizzle-orm';
import {
  createCustomerSchema,
  customerQuerySchema,
  type PaginatedResponse,
  type CustomerResponse,
} from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  validateQueryParams,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';
import { calculateCustomerTotalSessions } from '@/lib/package-service';
import { v4 as uuidv4 } from 'uuid';

// GET /api/customers - List customers with search and pagination
export const GET = withAuth(async (request: NextRequest, userSession) => {
  try {
    const trainerId = await getTrainerIdFromUser(userSession);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const queryValidation = validateQueryParams(request, customerQuerySchema);
    if (!queryValidation.success) {
      return createErrorResponse(
        queryValidation.error.error,
        queryValidation.error.message,
        400,
        queryValidation.error.details
      );
    }

    const { search, limit, offset, sortBy, sortOrder } = queryValidation.data;

    // Build where conditions
    const whereConditions = [eq(customers.trainerId, trainerId)];

    // Join user table for search
    if (search) {
      whereConditions.push(ilike(users.name, `%${search}%`));
    }

    // Build order by - ensure we have a valid column
    const validSortBy = sortBy || 'name';
    const orderDirection = sortOrder === 'desc' ? desc : asc;

    // Map sortBy to actual columns
    const sortColumns = {
      name: users.name,
      email: users.email,
      createdAt: users.createdAt,
    };

    const orderByColumn = sortColumns[validSortBy as keyof typeof sortColumns] || users.name;

    // Get total count
    const [totalResult] = await db
      .select({ count: count() })
      .from(customers)
      .innerJoin(users, eq(customers.userId, users.id))
      .where(and(...whereConditions));

    if (!totalResult) {
      throw new Error('Failed to fetch total count');
    }

    const total = totalResult.count;

    // Get customers with user fields
    const customerList = await db
      .select({
        id: customers.id,
        trainerId: customers.trainerId,
        parentName: customers.parentName,
        userId: customers.userId,
        name: users.name,
        email: users.email,
        phone: users.phone,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(customers)
      .innerJoin(users, eq(customers.userId, users.id))
      .where(and(...whereConditions))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit || 20)
      .offset(offset || 0);

    // Calculate total sessions for each customer
    const customersWithSessions = await Promise.all(
      customerList.map(async (customer) => {
        const totalSessions = await calculateCustomerTotalSessions(customer.id);
        return {
          ...customer,
          totalSessions,
        };
      })
    );

    const validLimit = limit || 20;
    const validOffset = offset || 0;

    const response: PaginatedResponse<CustomerResponse> = {
      data: customersWithSessions,
      pagination: {
        total,
        limit: validLimit,
        offset: validOffset,
        hasMore: validOffset + validLimit < total,
      },
    };

    return createSuccessResponse(response);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/customers - Create new customer
export const POST = withAuth(async (request: NextRequest, userSession) => {
  try {
    const trainerId = await getTrainerIdFromUser(userSession);
    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, createCustomerSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const customerData = bodyValidation.data;

    // Create user for customer
    const userId = uuidv4();
    const [newUser] = await db
      .insert(users)
      .values({
        id: userId,
        name: customerData.name,
        email: customerData.email || '',
        phone: customerData.phone || null,
        role: 'customer',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    if (!newUser) {
      throw new Error('Failed to create user for customer');
    }

    // Create customer
    const [newCustomer] = await db
      .insert(customers)
      .values({
        trainerId,
        userId: userId,
        parentName: customerData.parentName || null,
      })
      .returning();

    if (!newCustomer) {
      throw new Error('Failed to create customer');
    }

    // Calculate total sessions from packages
    const totalSessions = await calculateCustomerTotalSessions(newCustomer.id);

    const response: CustomerResponse = {
      id: newCustomer.id,
      trainerId: newCustomer.trainerId,
      name: newUser.name,
      email: newUser.email,
      phone: newUser.phone,
      totalSessions,
      parentName: newCustomer.parentName,
      createdAt: newUser.createdAt,
      updatedAt: newUser.updatedAt,
    };

    return createSuccessResponse(response, 201);
  } catch (error) {
    return handleApiError(error);
  }
});
