'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from '@workspace/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { RiLoader4Line } from '@remixicon/react';
import { LoaderFive } from '@/components/ui/loader';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import React from 'react';

// Schema copied from API route for consistency
const onboardingSchema = z.object({
  organizationName: z.string().min(1, 'Organization name is required'),
  organizationDescription: z.string().optional(),
  phone: z.string().optional(),
});

type OnboardingInput = z.infer<typeof onboardingSchema>;

export default function OnboardingPage() {
  const { data: session, isPending } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<OnboardingInput>({
    resolver: zodResolver(onboardingSchema),
    defaultValues: {
      organizationName: session?.user?.name ? `${session.user.name}'s Gym` : '',
      organizationDescription: '',
      phone: '',
    },
  });

  // Update default value if session loads after mount
  React.useEffect(() => {
    if (session?.user?.name) {
      form.setValue('organizationName', `${session.user.name}'s Gym`);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.name]);

  const handleCompleteOnboarding = async (data: OnboardingInput) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/trainer/onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast.success('Profile setup completed!');
        router.push('/');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to complete onboarding');
      }
    } catch (error) {
      console.error('❌ Error completing onboarding:', error);
      toast.error('An error occurred during onboarding');
    } finally {
      setIsLoading(false);
    }
  };

  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2 text-white">
          <LoaderFive text="Setting up your account..." />
        </div>
      </div>
    );
  }

  if (!session?.user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Welcome to Gym Scheduler!</CardTitle>
          <CardDescription>Let&apos;s set up your profile and organization</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleCompleteOnboarding)} className="space-y-4">
              <FormField
                control={form.control}
                name="organizationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Name *</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="e.g., Fitness Studio, Personal Training" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="organizationDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} placeholder="Tell us about your business..." rows={3} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input {...field} type="tel" placeholder="+60 123 4567" disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <RiLoader4Line className="mr-2 h-4 w-4 animate-spin" />
                    Setting up...
                  </>
                ) : (
                  'Complete Setup'
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
