import { ReactNode } from 'react';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export default async function AuthenticatedLayout({ children }: { children: ReactNode }) {
  // Forward cookies for authentication
  const cookieHeader = cookies().toString();

  // Check onboarding status server-side
  const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/trainer/profile`, {
    headers: { Cookie: cookieHeader },
    cache: 'no-store',
  });

  if (res.status !== 404) {
    // Onboarded, redirect to home
    redirect('/');
  }

  return <>{children}</>;
}
