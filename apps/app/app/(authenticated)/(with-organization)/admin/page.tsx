import type { Metadata } from 'next';
import { AppSidebar } from '@/components/app-sidebar';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SessionManagementDashboard } from '@/components/admin/session-management-dashboard';
import { MemberManagement } from '@/components/admin/member-management';
import { PackageManagement } from '@/components/admin/package-management';

export const metadata: Metadata = {
  title: 'Admin - LooLooFit',
  description: 'Session management and administrative tools',
};

export default function AdminPage() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <div className="flex flex-1 flex-col gap-4 p-2 pt-0">
          <div className="py-5 px-4">
            <div className="mb-6">
              <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              <p className="text-muted-foreground">Manage sessions, packages, and administrative settings</p>
            </div>

            <Tabs defaultValue="sessions" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="sessions">Session Management</TabsTrigger>
                <TabsTrigger value="packages">Package Management</TabsTrigger>
                <TabsTrigger value="members">Members Management</TabsTrigger>
              </TabsList>

              <TabsContent value="sessions" className="mt-6">
                <SessionManagementDashboard />
              </TabsContent>

              <TabsContent value="packages" className="mt-6">
                <PackageManagement />
              </TabsContent>

              <TabsContent value="members" className="mt-6">
                <MemberManagement />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
