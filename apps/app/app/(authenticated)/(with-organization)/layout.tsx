import { ReactNode } from 'react';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { CalendarProvider } from '@/components/event-calendar/calendar-context';
import { RedirectToSignIn } from '@workspace/auth';

export default async function AuthenticatedLayout({ children }: { children: ReactNode }) {
  // Forward cookies for authentication
  const cookieHeader = (await cookies()).toString();

  // Check onboarding status server-side
  const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/trainer/profile`, {
    headers: { Cook<PERSON>: cookieHeader },
    cache: 'no-store',
  });

  if (res.status === 404) {
    // Not onboarded, redirect to onboarding
    redirect('/onboarding');
  }

  return (
    <>
      <RedirectToSignIn />

      <CalendarProvider>{children}</CalendarProvider>
    </>
  );
}
