import { pgTable, text, timestamp, integer, decimal, boolean, uuid } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Trainers table
export const trainers = pgTable('trainers', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
});

// Customers table
export const customers = pgTable('customers', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id')
    .references(() => users.id, { onDelete: 'cascade' })
    .notNull(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  parentName: text('parent_name'),
});

// Packages table - admin-defined session packages
export const packages = pgTable('packages', {
  id: uuid('id').defaultRandom().primaryKey(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  name: text('name').notNull(),
  description: text('description'),
  sessionCount: integer('session_count').notNull(),
  price: decimal('price', { precision: 10, scale: 2 }).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Customer packages table - tracks packages assigned to customers
export const customerPackages = pgTable('customer_packages', {
  id: uuid('id').defaultRandom().primaryKey(),
  customerId: uuid('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  packageId: uuid('package_id')
    .references(() => packages.id)
    .notNull(),
  sessionsRemaining: integer('sessions_remaining').notNull(),
  purchaseDate: timestamp('purchase_date').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Credit transactions table for audit trail
export const creditTransactions = pgTable('credit_transactions', {
  id: uuid('id').defaultRandom().primaryKey(),
  customerId: uuid('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  type: text('type').notNull(), // purchase, deduction, refund, adjustment
  amount: integer('amount').notNull(), // positive for credits added, negative for credits deducted
  balanceBefore: integer('balance_before').notNull(),
  balanceAfter: integer('balance_after').notNull(),
  description: text('description').notNull(),
  relatedPackageId: uuid('related_package_id').references(() => customerPackages.id),
  relatedWorkoutId: uuid('related_workout_id').references(() => workouts.id),
  relatedParticipantId: uuid('related_participant_id').references(() => workoutParticipants.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Workouts table
export const workouts = pgTable('workouts', {
  id: uuid('id').defaultRandom().primaryKey(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  title: text('title').notNull(),
  description: text('description'),
  startTime: timestamp('start_time').notNull(),
  endTime: timestamp('end_time').notNull(),
  minParticipants: integer('min_participants').default(1).notNull(),
  maxParticipants: integer('max_participants').default(5).notNull(),
  status: text('status').default('scheduled').notNull(), // scheduled, confirmed, completed, cancelled
  location: text('location'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Workout participants table (many-to-many relationship)
export const workoutParticipants = pgTable('workout_participants', {
  id: uuid('id').defaultRandom().primaryKey(),
  workoutId: uuid('workout_id')
    .references(() => workouts.id)
    .notNull(),
  customerId: uuid('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  status: text('status').default('enrolled').notNull(), // enrolled, confirmed, cancelled
  enrolledAt: timestamp('enrolled_at').defaultNow().notNull(),
  confirmedAt: timestamp('confirmed_at'),
  creditDeducted: boolean('credit_deducted').default(false).notNull(),
});

// Better Auth tables
export const users = pgTable('users', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  phone: text('phone'),
  emailVerified: boolean('email_verified')
    .$defaultFn(() => false)
    .notNull(),
  image: text('image'),
  createdAt: timestamp('created_at')
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
  updatedAt: timestamp('updated_at')
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
  role: text('role').default('customer'), // 'trainer' or 'customer'
  // Admin plugin fields
  banned: boolean('banned'),
  banReason: text('ban_reason'),
  banExpires: timestamp('ban_expires'),
});

export const sessions = pgTable('sessions', {
  id: text('id').primaryKey(),
  expiresAt: timestamp('expires_at').notNull(),
  token: text('token').notNull().unique(),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  userId: text('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  activeOrganizationId: text('active_organization_id'),
  // Admin plugin fields
  impersonatedBy: text('impersonated_by'),
});

export const accounts = pgTable('accounts', {
  id: text('id').primaryKey(),
  accountId: text('account_id').notNull(),
  providerId: text('provider_id').notNull(),
  userId: text('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  idToken: text('id_token'),
  accessTokenExpiresAt: timestamp('access_token_expires_at'),
  refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
  scope: text('scope'),
  password: text('password'),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
});

export const verifications = pgTable('verifications', {
  id: text('id').primaryKey(),
  identifier: text('identifier').notNull(),
  value: text('value').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()),
  updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date()),
});

export const organizations = pgTable('organizations', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  slug: text('slug').unique(),
  logo: text('logo'),
  createdAt: timestamp('created_at').notNull(),
  metadata: text('metadata'),
});

export const members = pgTable('members', {
  id: text('id').primaryKey(),
  organizationId: text('organization_id')
    .notNull()
    .references(() => organizations.id, { onDelete: 'cascade' }),
  userId: text('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  role: text('role').default('member').notNull(),
  createdAt: timestamp('created_at').notNull(),
});

export const invitations = pgTable('invitations', {
  id: text('id').primaryKey(),
  organizationId: text('organization_id')
    .notNull()
    .references(() => organizations.id, { onDelete: 'cascade' }),
  email: text('email').notNull(),
  role: text('role'),
  status: text('status').default('pending').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  inviterId: text('inviter_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
});

export const organizationRelations = relations(organizations, ({ many }) => ({
  members: many(members),
  invitations: many(invitations),
}));

export const invitationRelations = relations(invitations, ({ one }) => ({
  organization: one(organizations, {
    fields: [invitations.organizationId],
    references: [organizations.id],
  }),
  inviter: one(users, {
    fields: [invitations.inviterId],
    references: [users.id],
  }),
}));

export const memberRelations = relations(members, ({ one }) => ({
  organization: one(organizations, {
    fields: [members.organizationId],
    references: [organizations.id],
  }),
  user: one(users, {
    fields: [members.userId],
    references: [users.id],
  }),
}));

export const accountRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const userRelations = relations(users, ({ one, many }) => ({
  sessions: many(sessions),
  accounts: many(accounts),
  members: many(members),
  invitations: many(invitations),
  trainer: one(trainers),
  customer: one(customers),
}));

export const sessionRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

export const trainersRelations = relations(trainers, ({ one, many }) => ({
  user: one(users, {
    fields: [trainers.userId],
    references: [users.id],
  }),
  customers: many(customers),
  workouts: many(workouts),
  packages: many(packages),
}));

export const customersRelations = relations(customers, ({ one, many }) => ({
  user: one(users, {
    fields: [customers.userId],
    references: [users.id],
  }),
  trainer: one(trainers, {
    fields: [customers.trainerId],
    references: [trainers.id],
  }),
  customerPackages: many(customerPackages),
  workoutParticipants: many(workoutParticipants),
  creditTransactions: many(creditTransactions),
}));

export const packagesRelations = relations(packages, ({ one, many }) => ({
  trainer: one(trainers, {
    fields: [packages.trainerId],
    references: [trainers.id],
  }),
  customerPackages: many(customerPackages),
}));

export const customerPackagesRelations = relations(customerPackages, ({ one }) => ({
  customer: one(customers, {
    fields: [customerPackages.customerId],
    references: [customers.id],
  }),
  package: one(packages, {
    fields: [customerPackages.packageId],
    references: [packages.id],
  }),
}));

export const creditTransactionsRelations = relations(creditTransactions, ({ one }) => ({
  customer: one(customers, {
    fields: [creditTransactions.customerId],
    references: [customers.id],
  }),
  relatedPackage: one(customerPackages, {
    fields: [creditTransactions.relatedPackageId],
    references: [customerPackages.id],
  }),
  relatedWorkout: one(workouts, {
    fields: [creditTransactions.relatedWorkoutId],
    references: [workouts.id],
  }),
  relatedParticipant: one(workoutParticipants, {
    fields: [creditTransactions.relatedParticipantId],
    references: [workoutParticipants.id],
  }),
}));

export const workoutsRelations = relations(workouts, ({ one, many }) => ({
  trainer: one(trainers, {
    fields: [workouts.trainerId],
    references: [trainers.id],
  }),
  participants: many(workoutParticipants),
}));

export const workoutParticipantsRelations = relations(workoutParticipants, ({ one }) => ({
  workout: one(workouts, {
    fields: [workoutParticipants.workoutId],
    references: [workouts.id],
  }),
  customer: one(customers, {
    fields: [workoutParticipants.customerId],
    references: [customers.id],
  }),
}));

export type Trainer = typeof trainers.$inferSelect;
export type NewTrainer = typeof trainers.$inferInsert;
export type Customer = typeof customers.$inferSelect;
export type NewCustomer = typeof customers.$inferInsert;
export type Package = typeof packages.$inferSelect;
export type NewPackage = typeof packages.$inferInsert;
export type CustomerPackage = typeof customerPackages.$inferSelect;
export type NewCustomerPackage = typeof customerPackages.$inferInsert;
export type CreditTransaction = typeof creditTransactions.$inferSelect;
export type NewCreditTransaction = typeof creditTransactions.$inferInsert;
export type Workout = typeof workouts.$inferSelect;
export type NewWorkout = typeof workouts.$inferInsert;
export type WorkoutParticipant = typeof workoutParticipants.$inferSelect;
export type NewWorkoutParticipant = typeof workoutParticipants.$inferInsert;
export type User = typeof users.$inferSelect;
export type Session = typeof sessions.$inferSelect;
export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;
export type Verification = typeof verifications.$inferSelect;
export type NewVerification = typeof verifications.$inferInsert;
export type Organization = typeof organizations.$inferSelect;
