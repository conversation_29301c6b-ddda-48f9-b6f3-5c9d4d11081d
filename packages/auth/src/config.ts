import { betterAuth, BetterAuthOptions } from 'better-auth';
import { nextCookies } from 'better-auth/next-js';
import { organization } from 'better-auth/plugins/organization';
import { admin } from 'better-auth/plugins';
import { createAccessControl } from 'better-auth/plugins/access';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { drizzle } from 'drizzle-orm/postgres-js';
import { eq, and, desc, asc, count, ilike } from 'drizzle-orm';
import postgres from 'postgres';
import * as schema from './schema';

// Access Control Configuration
const statement = {
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['read', 'update'],
} as const;

const ac = createAccessControl(statement);

// Define roles with permissions
export const adminRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['read', 'update'],
});

export const trainerRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['read'],
  organization: ['read'],
});

// Database connection
const connectionString = process.env.DATABASE_URL || 'postgresql://localhost:5432/loolookids';
const sql = postgres(connectionString);
const db = drizzle(sql, { schema });

const options = {
  database: drizzleAdapter(db, {
    provider: 'pg',
    usePlural: true,
  }),
  emailAndPassword: {
    enabled: true,
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  user: {
    additionalFields: {
      role: {
        type: 'string',
        defaultValue: 'customer',
      },
    },
  },
  plugins: [
    admin({
      ac,
      roles: {
        admin: adminRole,
        trainer: trainerRole,
      },
      defaultRole: 'customer',
      adminRoles: ['admin'],
    }),
    nextCookies(),
    organization({
      creatorRole: 'owner',
      slugGenerator: (name: string) =>
        name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, ''),
      async sendInvitationEmail(data) {
        console.debug('🚨 TODO!! 🚨');

        // const inviteLink = `https://example.com/accept-invitation/${data.id}`;

        // sendOrganizationInvitation({
        //   email: data.email,
        //   invitedByUsername: data.inviter.user.name,
        //   invitedByEmail: data.inviter.user.email,
        //   teamName: data.organization.name,
        //   inviteLink,
        // });
      },
    }),
  ],
  databaseHooks: {
    session: {
      create: {
        // find the first organization the user is a member of and set it as the active organization
        before: async (context) => {
          try {
            const organizations = await db
              .select({
                id: schema.organizations.id,
                name: schema.organizations.name,
              })
              .from(schema.organizations)
              .innerJoin(schema.members, eq(schema.members.organizationId, schema.organizations.id))
              .where(and(eq(schema.members.userId, context.userId)));

            // we should only have one or none
            // one if onboarded
            // none if not onboarded

            return {
              data: {
                ...context,
                activeOrganizationId: organizations?.[0]?.id,
              },
            };
          } catch (error) {
            console.log('❌ Error listing organizations:', error);

            return {
              data: context,
            };
          }
        },
      },
    },
  },
} satisfies BetterAuthOptions;

export const auth: ReturnType<typeof betterAuth<typeof options>> = betterAuth(options);

export type Session = typeof auth.$Infer.Session;
export type User = typeof auth.$Infer.Session.user;

export { db };
