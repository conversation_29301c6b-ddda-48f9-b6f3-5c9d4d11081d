'use client';

import { organizationClient, adminClient } from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import { createAccessControl } from 'better-auth/plugins/access';

// Access Control Configuration (must match server-side)
const statement = {
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['read', 'update'],
} as const;

const ac = createAccessControl(statement);

const adminRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['create', 'read', 'update', 'delete'],
  financial: ['create', 'read', 'update', 'delete'],
  organization: ['read', 'update'],
});

const trainerRole = ac.newRole({
  workout: ['create', 'read', 'update', 'delete', 'manage-participants'],
  customer: ['create', 'read', 'update', 'delete'],
  package: ['read'],
  organization: ['read'],
});

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_BETTER_AUTH_URL || 'http://localhost:3000',
  plugins: [
    organizationClient(),
    adminClient({
      ac,
      roles: {
        admin: adminRole,
        trainer: trainerRole,
      },
    }),
  ],
});

export const { signIn, signOut, signUp, useSession, $Infer } = authClient;

export type Session = typeof $Infer.Session;
export type User = typeof $Infer.Session.user;
