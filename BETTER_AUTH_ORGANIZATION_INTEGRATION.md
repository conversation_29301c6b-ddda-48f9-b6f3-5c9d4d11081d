# Better-Auth Organization Integration Guide

## Current Status

We've successfully integrated better-auth's organization plugin into the gym scheduler application. The basic setup is complete, but there are some areas that need to be finalized for full integration.

## What's Been Done

### ✅ **Completed**
1. **Plugin Integration**: Added the organization plugin to auth configuration
2. **Schema Updates**: Removed custom organizations table and updated trainers table
3. **Basic Onboarding**: Created onboarding flow for trainer profile setup
4. **API Structure**: Updated API endpoints to work with the new system

### 🔄 **Needs Completion**
1. **Organization Creation**: Currently using a simple ID generator instead of better-auth endpoints
2. **Organization Management UI**: Need to integrate better-auth UI components
3. **Member Management**: Implement proper member invitation and management
4. **Role Configuration**: Set up proper roles and permissions

## Next Steps to Complete Integration

### 1. **Complete Organization Creation in Onboarding**

Update `apps/app/app/api/trainer/onboarding/route.ts` to use better-auth's organization endpoints:

```typescript
// Instead of generating a simple ID, use better-auth's organization endpoint
const response = await fetch('/api/auth/organization', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    // Include auth headers
  },
  body: JSON.stringify({
    name: organizationName,
    slug: organizationName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
    metadata: organizationDescription ? { description: organizationDescription } : undefined,
  }),
});

if (!response.ok) {
  throw new Error('Failed to create organization');
}

const organization = await response.json();
const organizationId = organization.id;
```

### 2. **Add Organization Management UI**

Import and use better-auth's organization UI components:

```typescript
// In your organization management page
import { 
  CreateOrganizationDialog,
  OrganizationSwitcher,
  OrganizationMembersCard,
  OrganizationSettingsCards 
} from '@daveyplate/better-auth-ui';

// Use these components in your UI
```

### 3. **Configure Organization Roles**

Update the organization plugin configuration in `packages/auth/src/config.ts`:

```typescript
organization({
  creatorRole: 'owner',
  slugGenerator: (name: string) => name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
  roles: {
    owner: {
      // Full permissions
    },
    admin: {
      // Admin permissions
    },
    trainer: {
      // Trainer-specific permissions
    },
    member: {
      // Basic member permissions
    },
  },
  ac: {
    // Custom access control configuration
  },
}),
```

### 4. **Add Organization Context**

Create an organization context provider to manage active organization:

```typescript
// apps/app/components/organization/organization-provider.tsx
'use client';

import { createContext, useContext, useState } from 'react';
import { useSession } from '@workspace/auth';

interface OrganizationContextType {
  activeOrganizationId: string | null;
  setActiveOrganizationId: (id: string | null) => void;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: React.ReactNode }) {
  const { data: session } = useSession();
  const [activeOrganizationId, setActiveOrganizationId] = useState<string | null>(null);

  return (
    <OrganizationContext.Provider value={{ activeOrganizationId, setActiveOrganizationId }}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error('useOrganization must be used within OrganizationProvider');
  }
  return context;
}
```

### 5. **Update API Endpoints**

Modify existing API endpoints to work with organization context:

```typescript
// Update getTrainerIdFromUser to use active organization
export async function getTrainerIdFromUser(
  user: NonNullable<Awaited<ReturnType<typeof getAuthenticatedUser>>>,
  organizationId?: string
): Promise<string | null> {
  const { db, trainers } = await import('@workspace/auth/server');
  const { eq, and } = await import('drizzle-orm');

  try {
    const whereClause = organizationId 
      ? and(eq(trainers.userId, user.id), eq(trainers.organizationId, organizationId))
      : eq(trainers.userId, user.id);

    const [existingTrainer] = await db
      .select({ id: trainers.id })
      .from(trainers)
      .where(whereClause);

    return existingTrainer?.id || null;
  } catch (error) {
    console.error('Error finding trainer:', error);
    return null;
  }
}
```

### 6. **Add Organization Switching**

Create an organization switcher component:

```typescript
// apps/app/components/organization/organization-switcher.tsx
'use client';

import { useSession } from '@workspace/auth';
import { useOrganization } from './organization-provider';
import { OrganizationSwitcher as BetterAuthOrganizationSwitcher } from '@daveyplate/better-auth-ui';

export function OrganizationSwitcher() {
  const { data: session } = useSession();
  const { activeOrganizationId, setActiveOrganizationId } = useOrganization();

  if (!session?.user) return null;

  return (
    <BetterAuthOrganizationSwitcher
      onOrganizationChange={(orgId) => setActiveOrganizationId(orgId)}
      activeOrganizationId={activeOrganizationId}
    />
  );
}
```

### 7. **Update Layout**

Add organization provider to the layout:

```typescript
// apps/app/app/(authenticated)/layout.tsx
import { OrganizationProvider } from '@/components/organization/organization-provider';

export default function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  return (
    <RequireAuth>
      <OnboardingGuard>
        <OrganizationProvider>
          <CalendarProvider>{children}</CalendarProvider>
        </OrganizationProvider>
      </OnboardingGuard>
    </RequireAuth>
  );
}
```

## Testing the Integration

### 1. **Test Organization Creation**
```bash
# Register a new user
# Complete onboarding
# Verify organization is created in better-auth tables
```

### 2. **Test Organization Management**
```bash
# Access organization settings
# Add/remove members
# Test role permissions
```

### 3. **Test API Integration**
```bash
# Verify trainer endpoints work with organization context
# Test organization switching
# Check member permissions
```

## Database Migration

After completing the integration, you'll need to run migrations to create the better-auth organization tables:

```bash
# Generate new migration for better-auth organization tables
pnpm --filter @workspace/auth generate

# Run migrations
pnpm --filter @workspace/auth migrate
```

## Benefits of Better-Auth Organization Plugin

1. **Standardized**: Uses industry-standard organization management patterns
2. **Feature-Rich**: Built-in member management, invitations, roles, and permissions
3. **Maintained**: Actively maintained and updated by the better-auth team
4. **Extensible**: Easy to extend with custom roles and permissions
5. **UI Ready**: Comes with pre-built UI components
6. **API Complete**: Full REST API for all organization operations

## Troubleshooting

### Common Issues

1. **Organization not created**: Check better-auth configuration and permissions
2. **Member not added**: Verify role permissions and invitation flow
3. **API errors**: Ensure proper authentication headers are included
4. **UI not rendering**: Check component imports and better-auth-ui version

### Debug Steps

1. Check browser network tab for API calls
2. Verify better-auth organization tables exist in database
3. Check auth configuration for organization plugin setup
4. Review better-auth logs for organization-related errors

## Resources

- [Better-Auth Documentation](https://better-auth.com)
- [Organization Plugin Docs](https://better-auth.com/plugins/organization)
- [Better-Auth UI Components](https://github.com/daveyplate/better-auth-ui) 