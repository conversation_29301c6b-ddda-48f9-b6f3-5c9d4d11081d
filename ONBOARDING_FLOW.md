# Onboarding Flow Documentation

## Overview

The gym scheduler application now uses better-auth's official organization plugin for managing organizations and trainer profiles. This provides a more robust and standardized approach to organization management.

## Changes Made

### 1. **Better-Auth Organization Plugin Integration**
- Added the `organization` plugin to the auth configuration
- Configured with custom slug generator for organization URLs
- Set creator role to 'owner' for organization creators

### 2. **Schema Updates**
- Removed custom `organizations` table (now handled by better-auth)
- Updated `trainers` table to reference better-auth's organization system
- Simplified user table by removing custom organizationId field
- Updated all relations to work with the new structure

### 3. **API Updates**
- Updated `getTrainerIdFromUser` to work with the new organization system
- Modified onboarding API to create organizations using better-auth
- Simplified trainer profile creation process

## How It Works

### 1. **User Registration**
When a user registers through the auth system:
- A `user` record is created in the auth tables
- The user can create organizations using better-auth's organization system
- Organizations are managed through better-auth's built-in endpoints

### 2. **Organization Creation**
Users can create organizations through:
- Better-auth's organization endpoints (`/api/auth/organization/*`)
- The onboarding flow (creates organization and trainer profile)
- Better-auth UI components (if integrated)

### 3. **Trainer Profile Setup**
After organization creation:
- Trainer profile is created linking user to organization
- Additional trainer-specific data is stored in the `trainers` table
- Organization membership is managed by better-auth

## Better-Auth Organization Plugin Features

The organization plugin provides:
- **Organization Management**: Create, update, delete organizations
- **Member Management**: Add, remove, update member roles
- **Invitations**: Send and manage organization invitations
- **Role-Based Access Control**: Built-in permission system
- **Teams**: Optional team support within organizations
- **API Endpoints**: Complete REST API for organization operations

## API Endpoints Available

Better-auth provides these organization endpoints:
- `POST /api/auth/organization` - Create organization
- `GET /api/auth/organization` - List user's organizations
- `GET /api/auth/organization/[id]` - Get organization details
- `PUT /api/auth/organization/[id]` - Update organization
- `DELETE /api/auth/organization/[id]` - Delete organization
- `POST /api/auth/organization/[id]/member` - Add member
- `DELETE /api/auth/organization/[id]/member/[userId]` - Remove member
- `POST /api/auth/organization/invitation` - Send invitation
- `POST /api/auth/organization/invitation/[id]/accept` - Accept invitation

## Current Implementation Status

### ✅ **Completed**
- Better-auth organization plugin integration
- Schema updates to work with organization plugin
- Basic onboarding flow
- Trainer profile creation

### 🔄 **In Progress**
- Full integration with better-auth organization endpoints
- Organization management UI components
- Member invitation system

### 📋 **Next Steps**
1. **Complete Organization Integration**: Use better-auth's organization endpoints in onboarding
2. **Organization Management UI**: Add UI components for organization management
3. **Member Invitations**: Implement invitation system for adding trainers to organizations
4. **Role Management**: Configure proper roles and permissions
5. **Organization Switching**: Allow users to switch between organizations

## Configuration

The organization plugin is configured in `packages/auth/src/config.ts`:

```typescript
organization({
  creatorRole: 'owner',
  slugGenerator: (name: string) => name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
})
```

## Database Schema

The current schema works with better-auth's organization system:

```sql
-- Better-auth manages organizations automatically
-- Our trainers table extends the organization system
trainers (
  id, userId, organizationId, email, name, phone, ...
)

-- Better-auth user table (no custom organizationId)
user (
  id, name, email, role, ...
)
```

## Error Handling

- Organization creation failures are handled gracefully
- Trainer profile creation includes fallback mechanisms
- API endpoints return appropriate error responses
- Onboarding flow provides user-friendly error messages

## Testing the Flow

1. Register a new user at `/auth/sign-up`
2. User is redirected to `/onboarding`
3. Complete organization setup
4. Trainer profile is created
5. Access organization management features

## Troubleshooting

If organizations are not being created:

1. Check better-auth organization plugin configuration
2. Verify organization endpoints are accessible
3. Check database for organization tables created by better-auth
4. Ensure proper permissions for organization creation
5. Review organization plugin logs

## Future Enhancements

- **Multi-Organization Support**: Allow trainers to belong to multiple organizations
- **Organization Templates**: Pre-configured organization setups
- **Advanced Permissions**: Custom role definitions and permissions
- **Organization Analytics**: Usage statistics and reporting
- **Billing Integration**: Organization-based billing and subscriptions 